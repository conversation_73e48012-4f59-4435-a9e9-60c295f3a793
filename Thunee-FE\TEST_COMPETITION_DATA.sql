-- Test query to verify competition leaderboard data exists
USE [GoldRushThunee]
GO

-- Check if competition exists
SELECT 'Competition Check' AS TestType, * FROM Competitions WHERE Id = '4860c19d-e3f3-4e2d-b359-275527461bd6';

-- Check competition teams
SELECT 'Competition Teams' AS TestType, * FROM CompetitionTeams WHERE CompetitionId = '4860c19d-e3f3-4e2d-b359-275527461bd6';

-- Check users for the teams
SELECT 'Users Check' AS TestType, u.* 
FROM Users u 
WHERE u.Id IN (
    SELECT ct.Player1Id FROM CompetitionTeams ct WHERE ct.CompetitionId = '4860c19d-e3f3-4e2d-b359-275527461bd6'
    UNION
    SELECT ct.Player2Id FROM CompetitionTeams ct WHERE ct.CompetitionId = '4860c19d-e3f3-4e2d-b359-275527461bd6' AND ct.Player2Id IS NOT NULL
);

-- Test the leaderboard query directly
WITH RankedTeams AS (
    SELECT 
        ct.Id AS TeamId,
        ct.TeamName,
        u1.Username AS Player1Name,
        u2.Username AS Player2Name,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        (ct.MaxGames - ct.GamesPlayed) AS RemainingGames,
        CASE 
            WHEN ct.GamesPlayed = 0 THEN 0
            ELSE ct.Points
        END AS GamesWon,
        CASE 
            WHEN ct.GamesPlayed = 0 THEN 0
            ELSE (ct.GamesPlayed - ct.Points)
        END AS GamesLost,
        CASE 
            WHEN ct.GamesPlayed = 0 THEN 0.0
            ELSE ROUND((CAST(ct.Points AS FLOAT) / CAST(ct.GamesPlayed AS FLOAT)) * 100, 2)
        END AS WinRate,
        CASE 
            WHEN ct.GamesPlayed >= ct.MaxGames THEN 'Completed'
            WHEN ct.IsComplete = 1 THEN 'Active'
            ELSE 'Waiting for Partner'
        END AS Status,
        ROW_NUMBER() OVER (ORDER BY (ct.Points + ct.BonusPoints) DESC, ct.Points DESC, ct.GamesPlayed ASC) AS Rank
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = '4860c19d-e3f3-4e2d-b359-275527461bd6'
    AND ct.IsActive = 1
)
SELECT 
    'Leaderboard Result' AS TestType,
    Rank,
    TeamId,
    TeamName,
    Player1Name,
    Player2Name,
    GamesPlayed,
    Points,
    BonusPoints,
    TotalPoints,
    MaxGames,
    RemainingGames,
    GamesWon,
    GamesLost,
    WinRate,
    Status
FROM RankedTeams
ORDER BY Rank;

-- Test the stored procedure directly
EXEC SP_GetCompetitionLeaderboard @CompetitionId = '4860c19d-e3f3-4e2d-b359-275527461bd6', @PageSize = 20, @PageNumber = 1;
