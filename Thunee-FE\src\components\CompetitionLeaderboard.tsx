import React, { useState, useEffect } from 'react';
import { X, Trophy, Users, Target, TrendingUp, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { apiService } from '@/services/api';

interface CompetitionLeaderboardEntry {
  rank: number;
  teamId: string;
  teamName: string;
  player1Name: string;
  player2Name: string;
  points: number;
  bonusPoints: number;
  totalPoints: number;
  gamesPlayed: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  maxGames: number;
  remainingGames: number;
  status: string;
}

interface CompetitionLeaderboardProps {
  isOpen: boolean;
  onClose: () => void;
  competitionId: string;
  competitionName: string;
}

export default function CompetitionLeaderboard({
  isOpen,
  onClose,
  competitionId,
  competitionName
}: CompetitionLeaderboardProps) {
  const [leaderboard, setLeaderboard] = useState<CompetitionLeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (isOpen) {
      fetchLeaderboard();
    }
  }, [isOpen, competitionId, currentPage]);

  const fetchLeaderboard = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getCompetitionLeaderboard(competitionId, currentPage, 20);
      
      if (response.success && response.data) {
        setLeaderboard(response.data.teams || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
      } else {
        setError('Failed to load leaderboard data');
      }
    } catch (error) {
      console.error('Error fetching competition leaderboard:', error);
      setError('Failed to load leaderboard. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Trophy className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Trophy className="h-5 w-5 text-amber-700" />;
      default:
        return <span className="text-lg font-bold text-gray-400">#{rank}</span>;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'completed':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'waiting for partner':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1A1A1A] border border-[#333333] rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-[#333333]">
          <div>
            <h2 className="text-2xl font-bold text-[#E1C760]">Competition Leaderboard</h2>
            <p className="text-gray-400 text-sm mt-1">{competitionName}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E1C760]"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-400 mb-4">{error}</p>
              <Button onClick={fetchLeaderboard} className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90">
                Try Again
              </Button>
            </div>
          ) : leaderboard.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-400">No teams in this competition yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Desktop Table Header */}
              <div className="hidden lg:grid lg:grid-cols-12 gap-4 text-sm font-semibold text-gray-400 border-b border-[#333333] pb-2">
                <div className="col-span-1">Rank</div>
                <div className="col-span-3">Team & Players</div>
                <div className="col-span-2">Games</div>
                <div className="col-span-2">Points</div>
                <div className="col-span-2">Performance</div>
                <div className="col-span-2">Status</div>
              </div>

              {/* Leaderboard Entries */}
              {leaderboard.map((entry) => (
                <div
                  key={entry.teamId}
                  className="bg-[#2A2A2A] border border-[#333333] rounded-lg p-4 hover:border-[#E1C760] transition-colors"
                >
                  {/* Mobile Layout */}
                  <div className="lg:hidden space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getRankIcon(entry.rank)}
                        <div>
                          <h3 className="font-semibold text-white">{entry.teamName}</h3>
                          <p className="text-sm text-gray-400">
                            {entry.player1Name} & {entry.player2Name}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-[#E1C760]">{entry.totalPoints}</div>
                        <div className="text-xs text-gray-400">Total Points</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-400">Games</div>
                        <div className="text-white">{entry.gamesPlayed}/{entry.maxGames}</div>
                      </div>
                      <div>
                        <div className="text-gray-400">Win Rate</div>
                        <div className="text-white">{entry.winRate.toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-gray-400">Points</div>
                        <div className="text-white">{entry.points} + {entry.bonusPoints} bonus</div>
                      </div>
                      <div>
                        <div className="text-gray-400">Record</div>
                        <div className="text-white">{entry.gamesWon}W - {entry.gamesLost}L</div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(entry.status)}`}>
                        {entry.status}
                      </span>
                      <span className="text-xs text-gray-400">
                        {entry.remainingGames} games remaining
                      </span>
                    </div>
                  </div>

                  {/* Desktop Layout */}
                  <div className="hidden lg:grid lg:grid-cols-12 gap-4 items-center">
                    <div className="col-span-1 flex justify-center">
                      {getRankIcon(entry.rank)}
                    </div>
                    
                    <div className="col-span-3">
                      <h3 className="font-semibold text-white">{entry.teamName}</h3>
                      <p className="text-sm text-gray-400">
                        {entry.player1Name} & {entry.player2Name}
                      </p>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="text-white">{entry.gamesPlayed}/{entry.maxGames}</div>
                      <div className="text-xs text-gray-400">{entry.remainingGames} remaining</div>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="text-lg font-bold text-[#E1C760]">{entry.totalPoints}</div>
                      <div className="text-xs text-gray-400">{entry.points} + {entry.bonusPoints} bonus</div>
                    </div>
                    
                    <div className="col-span-2">
                      <div className="text-white">{entry.gamesWon}W - {entry.gamesLost}L</div>
                      <div className="text-xs text-gray-400">{entry.winRate.toFixed(1)}% win rate</div>
                    </div>
                    
                    <div className="col-span-2">
                      <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(entry.status)}`}>
                        {entry.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="border-[#333333] text-gray-400 hover:text-white"
              >
                Previous
              </Button>
              <span className="flex items-center px-3 text-sm text-gray-400">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="border-[#333333] text-gray-400 hover:text-white"
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
