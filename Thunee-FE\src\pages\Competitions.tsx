"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Competition } from "@/types/leaderboard";
import { leaderboardService } from "@/services/leaderboardService";
import { apiService } from "@/services/api";
import { ArrowLeft, Trophy, Calendar, Users, AlertCircle, Play, BarChart3 } from "lucide-react";
import BurgerMenu from "@/components/BurgerMenu";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { format } from "date-fns";
import { useAuthStore } from "@/store/authStore";
import CompetitionJoinModal from "@/components/CompetitionJoinModal";
import CompetitionLeaderboard from "@/components/CompetitionLeaderboard";

export default function Competitions() {
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [loading, setLoading] = useState(true);
  const [joiningCompetition, setJoiningCompetition] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [competitionStatuses, setCompetitionStatuses] = useState<Record<string, any>>({});
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    const fetchCompetitions = async () => {
      try {
        const data = await leaderboardService.getCompetitions();
        setCompetitions(data);

        // Fetch competition statuses for authenticated users
        if (isAuthenticated) {
          const statuses: Record<string, any> = {};
          for (const competition of data) {
            try {
              const status = await apiService.getCompetitionStatus(competition.id);
              statuses[competition.id] = status;
            } catch (error) {
              console.error(`Error fetching status for competition ${competition.id}:`, error);
            }
          }
          setCompetitionStatuses(statuses);
        }
      } catch (error) {
        console.error("Error fetching competitions:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompetitions();
  }, [isAuthenticated]);

  const getStatusColor = (status: Competition["status"]) => {
    switch (status) {
      case "active":
        return "text-green-500";
      case "upcoming":
        return "text-blue-500";
      case "completed":
        return "text-gray-400";
      default:
        return "text-white";
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const handleJoinCompetition = async (competition: Competition, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent navigation to competition details

    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    setSelectedCompetition(competition);
    setShowJoinModal(true);
  };

  const handleJoinSuccess = async () => {
    // Refresh competitions and statuses
    try {
      const data = await leaderboardService.getCompetitions();
      setCompetitions(data);

      if (selectedCompetition) {
        const status = await apiService.getCompetitionStatus(selectedCompetition.id);
        setCompetitionStatuses(prev => ({
          ...prev,
          [selectedCompetition.id]: status
        }));
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  const handleResumeCompetition = (competitionId: string) => {
    // Navigate to competition lobby
    navigate(`/competitions/${competitionId}/lobby`);
  };

  const getCompetitionActionButton = (competition: Competition) => {
    const status = competitionStatuses[competition.id];

    if (!status) {
      return null; // Loading status
    }

    if (status.status === 'waiting_for_partner') {
      return (
        <Button
          disabled
          className="bg-yellow-600 text-white cursor-not-allowed text-sm"
        >
          Waiting for Partner
        </Button>
      );
    }

    if (status.status === 'ready_to_play' || status.status === 'in_progress') {
      return (
        <Button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleResumeCompetition(competition.id);
          }}
          className="bg-green-600 hover:bg-green-700 text-white text-sm"
        >
          <Play className="h-4 w-4 mr-1" />
          Resume
        </Button>
      );
    }

    if (status.status === 'completed') {
      return (
        <Button
          disabled
          className="bg-gray-600 text-gray-300 cursor-not-allowed text-sm"
        >
          Completed
        </Button>
      );
    }

    if (status.canJoin) {
      return (
        <Button
          onClick={(e) => handleJoinCompetition(competition, e)}
          className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90 text-sm"
        >
          Join Competition
        </Button>
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen bg-dark text-white flex flex-col relative">
      {/* BurgerMenu */}
      <BurgerMenu />

      {/* Main Content */}
      <div className="flex flex-col items-center px-6 pb-16 space-y-6 mt-16 overflow-y-auto h-[calc(100vh-4rem)] z-10 relative">
        {/* Header */}
        <div className="w-full flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
            onClick={() => navigate("/")}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-[#E1C760]">Competitions</h1>
          <div className="w-[60px]"></div> {/* Spacer for alignment */}
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-4 bg-red-900/50 border border-red-500">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-sm">{error}</AlertDescription>
          </Alert>
        )}

        {/* Competitions List */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E1C760]"></div>
          </div>
        ) : (
          <div className="w-full space-y-4">
            {competitions.map((competition) => {
              const status = competitionStatuses[competition.id];
              const hasActionButton = isAuthenticated && competition.status === 'active' && status &&
                (status.status === 'ready_to_play' || status.status === 'in_progress' || status.canJoin);

              return (
                <div
                  key={competition.id}
                  className={`bg-[#1A1A1A] border border-[#333333] rounded-lg p-4 hover:border-[#E1C760] transition-colors ${
                    hasActionButton ? '' : 'cursor-pointer'
                  }`}
                  onClick={hasActionButton ? undefined : () => navigate(`/competitions/${competition.id}`)}
                >

                <div className="flex justify-between items-start mb-2">
                  <h2 className="text-xl font-semibold text-[#E1C760]">
                    {competition.name}
                  </h2>
                  <span
                    className={`text-sm px-2 py-1 rounded-full border ${getStatusColor(
                      competition.status
                    )}`}
                  >
                    {competition.status.charAt(0).toUpperCase() +
                      competition.status.slice(1)}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-400 mb-3">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formatDate(competition.startDate)} - {formatDate(competition.endDate)}
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {competition.currentTeams}/{competition.maxTeams} teams
                  </div>
                </div>

                <p className="text-sm text-gray-300 mb-4">
                  {competition.description || "No description available."}
                </p>

                <div className="flex flex-wrap gap-3 mb-4">
                  <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                    <Trophy className="h-4 w-4 mr-1 text-yellow-500" />
                    <span className="text-sm">1st: {competition.prizes.first}</span>
                  </div>
                  <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                    <Trophy className="h-4 w-4 mr-1 text-gray-400" />
                    <span className="text-sm">2nd: {competition.prizes.second}</span>
                  </div>
                  <div className="flex items-center bg-[#2A2A2A] rounded-md px-3 py-1">
                    <Trophy className="h-4 w-4 mr-1 text-amber-700" />
                    <span className="text-sm">3rd: {competition.prizes.third}</span>
                  </div>
                </div>

                {/* Additional Action Buttons */}
                <div className="flex gap-2 mb-4">
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      navigate(`/competitions/${competition.id}/brackets`);
                    }}
                    variant="outline"
                    size="sm"
                    className="border-blue-500 bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 hover:text-blue-700 dark:border-blue-500/50 dark:bg-blue-500/5 dark:text-blue-400 dark:hover:bg-blue-500/15 dark:hover:text-blue-300"
                  >
                    <Trophy className="h-4 w-4 mr-1" />
                    Brackets
                  </Button>

                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setSelectedCompetition(competition);
                      setShowLeaderboard(true);
                    }}
                    variant="outline"
                    size="sm"
                    className="border-[#E1C760] bg-[#E1C760]/10 text-[#E1C760] hover:bg-[#E1C760]/20 hover:text-[#E1C760]"
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    View Leaderboard
                  </Button>
                </div>

                {/* Competition Action Button */}
                {isAuthenticated && competition.status === 'active' && (
                  <div
                    className="flex justify-end"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    {getCompetitionActionButton(competition)}
                  </div>
                )}
              </div>
              );
            })}

            {competitions.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-400">No competitions available at the moment.</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Competition Join Modal */}
      {selectedCompetition && (
        <CompetitionJoinModal
          isOpen={showJoinModal}
          onClose={() => {
            setShowJoinModal(false);
            setSelectedCompetition(null);
          }}
          competitionId={selectedCompetition.id}
          competitionName={selectedCompetition.name}
          onSuccess={handleJoinSuccess}
        />
      )}

      {/* Competition Leaderboard Modal */}
      {selectedCompetition && (
        <CompetitionLeaderboard
          isOpen={showLeaderboard}
          onClose={() => {
            setShowLeaderboard(false);
            setSelectedCompetition(null);
          }}
          competitionId={selectedCompetition.id}
          competitionName={selectedCompetition.name}
        />
      )}
    </div>
  );
}
