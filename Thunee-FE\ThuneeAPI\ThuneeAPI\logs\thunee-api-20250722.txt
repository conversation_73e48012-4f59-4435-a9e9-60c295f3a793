2025-07-22 08:50:23.785 +02:00 [INF] Starting Thunee API Server
2025-07-22 08:50:30.380 +02:00 [INF] Login attempt for username: <PERSON><PERSON>an123
2025-07-22 08:50:32.017 +02:00 [INF] User logged in successfully: "ee9511df-a34b-46ec-b0b7-8bcd0e24373f"
2025-07-22 08:50:32.629 +02:00 [INF] Game settings retrieved successfully
2025-07-22 08:50:33.127 +02:00 [INF] Game settings retrieved successfully
2025-07-22 08:51:43.222 +02:00 [INF] Game settings retrieved successfully
2025-07-22 08:51:43.888 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:09:01.670 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
2025-07-22 09:09:02.175 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
2025-07-22 09:09:04.744 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:09:05.261 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:09:06.884 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:09:07.394 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:09:07.919 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
2025-07-22 09:09:08.622 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserialize0d24ddac-5a1d-4489-94bb-4a97cb40601a(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
