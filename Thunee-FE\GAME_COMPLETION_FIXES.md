# Game Completion and Competition Scoring Fixes

## Issues Fixed

### 1. ✅ Ball Scores in Game Completion
**Problem**: Game completion was passing `team1Score: 0, team2Score: 0` instead of actual ball scores (e.g., 0 vs 13).

**Root Cause**: The `saveGameEndResult` function in `gameDataUtils.js` was incorrectly using `gameEndData.finalScores` as point scores instead of ball scores.

**Fix Applied**: Updated `gameDataUtils.js` lines 21-35 to correctly use ball scores:
```javascript
const team1BallsWon = gameEndData.finalScores?.team1 || gameEndData.ballScores?.team1 || lobby.ballScores?.team1 || 0;
const team2BallsWon = gameEndData.finalScores?.team2 || gameEndData.ballScores?.team2 || lobby.ballScores?.team2 || 0;

const competitionGameResult = {
  winningTeam: gameEndData.winner,
  team1Score: team1BallsWon, // For competition scoring, use ball counts as scores
  team2Score: team2BallsWon,
  team1BallsWon: team1BallsWon,
  team2BallsWon: team2BallsWon,
  ballDifference: Math.abs(team1BallsWon - team2BallsWon),
  // ...
};
```

### 2. ✅ Missing dealer_updated Events After Special Ball Completions
**Problem**: After Khanka, Double, or Thunee success, the dealer rotation worked on the server but the frontend didn't receive the `dealer_updated` event, causing the wrong player to see the shuffle button.

**Root Cause**: The special ball completion handlers were missing the `dealer_updated` event emission that the normal ball completion had.

**Fix Applied**: Added `dealer_updated` event emission to all three scenarios in `server/index.js`:
- Khanka outcome (lines 5558-5583)
- Double outcome (lines 5745-5763)  
- Thunee success (lines 5949-5974)

Each fix includes proper trump selector calculation and error handling.

## Issues Requiring Database Update

### 3. ⚠️ Missing SP_RecordGameCompletion Stored Procedure
**Problem**: The logs show `Stored procedure 'SP_RecordGameCompletion' not found in resources`.

**Root Cause**: The `DATABASE_UPDATE_REMOVE_CARD_STORAGE.sql` script hasn't been run on the database.

**Fix Required**: Run the database update script to create the missing stored procedure.

**How to Fix**:
1. **Option A**: Run `Run_Database_Update.bat` (created for you)
2. **Option B**: Run `Run_Database_Update.ps1` in PowerShell
3. **Option C**: Manually run `DATABASE_UPDATE_REMOVE_CARD_STORAGE.sql` in SSMS

**After Database Update**: Restart the ThuneeAPI server to use the new stored procedure.

## Competition Scoring

The competition scoring logic appears correct:
- 1 point for winning
- +1 bonus point for winning by 6+ ball difference
- Team statistics are updated properly

The API calls are working (status 200), but there might be issues with team ID resolution in some cases.

## Testing the Fixes

### Test Dealer Rotation Fix:
1. Start a game and play until you can call Khanka/Double/Thunee
2. Successfully complete the special call
3. Verify the correct player sees the "SHUFFLE" button
4. Verify no "Only the dealer can shuffle the deck" errors

### Test Game Completion Fix:
1. Complete a competition game
2. Check the logs for correct ball scores (not 0-0)
3. Verify team points are awarded correctly
4. Check that the game status is updated to "completed"

## Files Modified

1. `server/utils/gameDataUtils.js` - Fixed ball score passing
2. `server/index.js` - Added missing dealer_updated events
3. `Run_Database_Update.ps1` - Created database update script
4. `Run_Database_Update.bat` - Created batch file for database update

## Next Steps

1. **Run the database update** using one of the provided scripts
2. **Restart the ThuneeAPI server** after database update
3. **Test the fixes** with a complete game
4. **Monitor logs** for any remaining issues

The server is currently running with the code fixes applied. The database update is the only remaining step to fully resolve all issues.
