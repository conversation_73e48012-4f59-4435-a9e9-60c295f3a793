
namespace ThuneeAPI.Application.DTOs;

public class AdminUserDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool IsVerified { get; set; }
    public bool IsActive { get; set; }
    public bool IsAdmin { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class AdminUpdateUserDto
{
    public string? Username { get; set; }
    public string? Email { get; set; }
    public bool? IsVerified { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsAdmin { get; set; }
}

public class ChangeUserPasswordDto
{
    public string NewPassword { get; set; } = string.Empty;
}

public class CompetitionEmailDto
{
    public string Subject { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}

public class AdminCompetitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public int MaxTeams { get; set; }
    public int CurrentTeams { get; set; }
    public decimal EntryFee { get; set; }
    public decimal PrizeFirst { get; set; }
    public decimal PrizeSecond { get; set; }
    public decimal PrizeThird { get; set; }
    public decimal TotalPrizePool { get; set; }
    public string Rules { get; set; } = string.Empty;
    public bool IsPublic { get; set; }
    public bool AllowSpectators { get; set; }
    public long MaxGamesPerTeam { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class AdminCompetitionTeamDto
{
    public Guid Id { get; set; }
    public Guid CompetitionId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player1Id { get; set; }
    public string Player1Username { get; set; } = string.Empty;
    public string Player1Email { get; set; } = string.Empty;
    public Guid Player2Id { get; set; }
    public string Player2Username { get; set; } = string.Empty;
    public string Player2Email { get; set; } = string.Empty;
    public string InviteCode { get; set; } = string.Empty;
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int MaxGames { get; set; }
    public bool IsActive { get; set; }
    public bool IsComplete { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

public class AdminGameDto
{
    public Guid Id { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public Guid? CompetitionId { get; set; }
    public string? CompetitionName { get; set; }
    public string Team1Name { get; set; } = string.Empty;
    public string Team2Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    public int? WinnerTeam { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; }
}

