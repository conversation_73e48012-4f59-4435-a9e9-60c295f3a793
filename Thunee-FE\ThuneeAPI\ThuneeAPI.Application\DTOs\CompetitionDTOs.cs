
namespace ThuneeAPI.Application.DTOs;

public class CompetitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public int MaxTeams { get; set; }
    public int CurrentTeams { get; set; }
    public decimal EntryFee { get; set; }
    public PrizesDto Prizes { get; set; } = new();
    public decimal? TotalPrizePool { get; set; }
    public string? Rules { get; set; }
    public bool IsPublic { get; set; }
    public bool AllowSpectators { get; set; }
    public DateTime CreatedAt { get; set; }

    // Phase Management
    public string Phase { get; set; } = "Leaderboard";
    public DateTime? PhaseEndDate { get; set; }
    public long? MaxGamesPerPhase { get; set; }
}

public class PrizesDto
{
    public string? First { get; set; }
    public string? Second { get; set; }
    public string? Third { get; set; }
}

public class CreateCompetitionDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int MaxTeams { get; set; } = 32;
    public decimal EntryFee { get; set; } = 0;
    public string? PrizeFirst { get; set; }
    public string? PrizeSecond { get; set; }
    public string? PrizeThird { get; set; }
    public decimal? TotalPrizePool { get; set; }
    public string? Rules { get; set; }
    public bool IsPublic { get; set; } = true;
    public bool AllowSpectators { get; set; } = true;
}

public class UpdateCompetitionDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? MaxTeams { get; set; }
    public decimal? EntryFee { get; set; }
    public string? PrizeFirst { get; set; }
    public string? PrizeSecond { get; set; }
    public string? PrizeThird { get; set; }
    public decimal? TotalPrizePool { get; set; }
    public string? Rules { get; set; }
    public bool? IsPublic { get; set; }
    public bool? AllowSpectators { get; set; }
}

public class CompetitionTeamDto
{
    public Guid Id { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public PlayerDto Player1 { get; set; } = new();
    public PlayerDto? Player2 { get; set; } // Nullable until partner joins
    public string InviteCode { get; set; } = string.Empty;
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int MaxGames { get; set; }
    public bool IsComplete { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime? CompletedAt { get; set; }

    // Phase Management
    public string Phase { get; set; } = "Leaderboard";
    public bool IsEliminated { get; set; } = false;
    public bool AdvancedToNextPhase { get; set; } = false;
    public DateTime? PhaseEliminatedAt { get; set; }
}

public class CreateCompetitionTeamDto
{
    public Guid CompetitionId { get; set; }
    public string TeamName { get; set; } = string.Empty;
}

public class JoinCompetitionTeamDto
{
    public string InviteCode { get; set; } = string.Empty;
}

public class JoinCompetitionDto
{
    public Guid CompetitionId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player2Id { get; set; }
}

public class LeaderboardEntryDto
{
    public Guid Id { get; set; }
    public Guid PlayerId { get; set; }
    public string PlayerName { get; set; } = string.Empty;
    public int Score { get; set; }
    public int Rank { get; set; }
    public int GamesPlayed { get; set; }
    public int GamesWon { get; set; }
    public double WinRate { get; set; }
}

public class LeaderboardResponseDto
{
    public List<LeaderboardEntryDto> Data { get; set; } = new();
    public PaginationDto Pagination { get; set; } = new();
    public FilterDto Filters { get; set; } = new();
}

public class PaginationDto
{
    public int CurrentPage { get; set; }
    public int TotalPages { get; set; }
    public int TotalItems { get; set; }
    public int ItemsPerPage { get; set; }
}

public class FilterDto
{
    public string TimeFrame { get; set; } = string.Empty;
    public string SortBy { get; set; } = string.Empty;
}

public class CompetitionStatusDto
{
    public Guid CompetitionId { get; set; }
    public string CompetitionName { get; set; } = string.Empty;
    public CompetitionTeamDto? Team { get; set; }
    public bool HasTeam { get; set; }
    public bool CanJoin { get; set; }
    public bool CanResume { get; set; }
    public string Status { get; set; } = string.Empty; // waiting_for_partner, ready_to_play, in_progress, completed
}

public class CompetitionLeaderboardEntryDto
{
    public Guid TeamId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public string Player1Name { get; set; } = string.Empty;
    public string Player2Name { get; set; } = string.Empty;
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int TotalPoints { get; set; }
    public int GamesPlayed { get; set; }
    public int GamesWon { get; set; }
    public int GamesLost { get; set; }
    public double WinRate { get; set; }
    public int MaxGames { get; set; }
    public int RemainingGames { get; set; }
    public string Status { get; set; } = string.Empty;
    public int Rank { get; set; }
}

public class CompetitionLeaderboardResponseDto
{
    public List<CompetitionLeaderboardEntryDto> Teams { get; set; } = new();
    public PaginationDto Pagination { get; set; } = new();
}

public class CompetitionGameResultDto
{
    public Guid GameId { get; set; }
    public Guid CompetitionId { get; set; }
    public Guid Team1Id { get; set; }
    public Guid Team2Id { get; set; }
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    public int WinnerTeam { get; set; } // 1 or 2
}

// Phase Management DTOs
public class CompetitionPhaseLobbyDto
{
    public Guid Id { get; set; }
    public Guid CompetitionId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public string LobbyCode { get; set; } = string.Empty;
    public Guid CreatedByAdminId { get; set; }
    public string CreatedByAdminUsername { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public List<CompetitionPhaseLobbyTeamDto> Teams { get; set; } = new();
}

public class CompetitionPhaseLobbyTeamDto
{
    public Guid Id { get; set; }
    public Guid LobbyId { get; set; }
    public Guid CompetitionTeamId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public bool IsWinner { get; set; }
    public DateTime? EliminatedAt { get; set; }
}

public class CreateCompetitionPhaseLobbyDto
{
    public Guid CompetitionId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public List<Guid> TeamIds { get; set; } = new();
}

public class CompetitionTeamPhaseStatsDto
{
    public Guid Id { get; set; }
    public Guid CompetitionTeamId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int GamesPlayed { get; set; }
    public int BallsWon { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class BracketDto
{
    public string Phase { get; set; } = string.Empty;
    public List<BracketMatchDto> Matches { get; set; } = new();
}

public class BracketMatchDto
{
    public Guid LobbyId { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public CompetitionTeamDto? Team1 { get; set; }
    public CompetitionTeamDto? Team2 { get; set; }
    public CompetitionTeamDto? Winner { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime? CompletedAt { get; set; }
}

