import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useGameStore } from '../store/gameStore';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Trophy, Clock, Target, Users } from 'lucide-react';
import "../styles/GameEndDisplay.css";

interface GameEndDisplayProps {
  isVisible: boolean;
  onClose: () => void;
  competitionId?: string | null;
}

export default function GameEndDisplay({ isVisible, onClose, competitionId }: GameEndDisplayProps) {
  const { winner, teamNames, gameHistory, ballScores } = useGameStore();
  const [showHistory, setShowHistory] = useState(false);
  const navigate = useNavigate();

  // Debug logging
  console.log('GameEndDisplay - competitionId:', competitionId);

  if (!isVisible || !winner || !gameHistory) {
    return null;
  }

  const winnerTeamName = teamNames[winner];
  const loserTeam = winner === 1 ? 2 : 1;
  const loserTeamName = teamNames[loserTeam];
  const winnerBalls = ballScores[`team${winner}`];
  const loserBalls = ballScores[`team${loserTeam}`];

  // Format duration
  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Get special call display text
  const getSpecialCallText = (specialCall: string | null, details: any) => {
    if (!specialCall) return null;
    
    switch (specialCall) {
      case 'Double':
        return 'Double';
      case 'Khanak':
        return 'Khanak';
      case 'Four Ball':
        return '4 Ball';
      case 'Timeout':
        return 'Timeout';
      case 'Call and Lost':
        return 'Call & Lost';
      default:
        return specialCall;
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 game-end-container"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="bg-gray-900 rounded-lg border-2 border-[#E1C760] max-w-4xl w-full max-h-[90vh] overflow-hidden game-end-card"
          >
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center justify-center mb-4">
                <Trophy className="w-12 h-12 text-[#E1C760] mr-4" />
                <div className="text-center">
                  <h1 className="text-3xl font-bold text-[#E1C760] mb-2">Game Complete!</h1>
                  <h2 className="text-2xl font-semibold text-white">
                    {winnerTeamName} Wins!
                  </h2>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6 mb-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-[#E1C760] mb-2">Final Score</h3>
                  <div className="text-2xl font-bold text-white">
                    {winnerTeamName}: {winnerBalls} balls
                  </div>
                  <div className="text-xl text-gray-300">
                    {loserTeamName}: {loserBalls} balls
                  </div>
                </div>

                <div className="text-center">
                  <h3 className="text-lg font-semibold text-[#E1C760] mb-2">Game Stats</h3>
                  <div className="flex items-center justify-center text-white mb-1">
                    <Clock className="w-4 h-4 mr-2" />
                    Duration: {formatDuration(gameHistory.duration)}
                  </div>
                  <div className="flex items-center justify-center text-white">
                    <Target className="w-4 h-4 mr-2" />
                    Total Balls: {gameHistory.totalBalls}
                  </div>
                </div>
              </div>

              <div className="flex justify-center gap-4">
                <Button
                  onClick={() => setShowHistory(!showHistory)}
                  className="bg-[#E1C760] text-black hover:bg-[#c9b052]"
                >
                  {showHistory ? 'Hide' : 'Show'} Game History
                </Button>
                <Button
                  onClick={() => {
                    onClose();
                    // If this is a competition game, redirect to the competitions page
                    if (competitionId) {
                      console.log('Redirecting to competitions page for competition:', competitionId);
                      window.location.href = 'http://localhost:5173/competitions';
                    }
                  }}
                  variant="outline"
                  className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760] hover:text-black"
                >
                  {competitionId ? 'Back to Competitions' : 'Close'}
                </Button>
              </div>
            </div>

            {showHistory && (
              <div className="p-6 max-h-96 overflow-y-auto">
                <h3 className="text-xl font-semibold text-[#E1C760] mb-4 flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  Ball-by-Ball History
                </h3>
                
                <div className="space-y-3">
                  {gameHistory.balls.map((ball, index) => (
                    <Card key={index} className="bg-gray-800 border-gray-600 p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="text-lg font-semibold text-[#E1C760]">
                            Ball {ball.ballNumber}
                          </div>
                          <div className="text-white">
                            Winner: {teamNames[ball.winner]}
                          </div>
                          <div className="text-gray-300">
                            Balls Awarded: {ball.ballsAwarded}
                          </div>
                          {ball.specialCall && (
                            <div className="px-2 py-1 bg-[#E1C760] text-black text-sm rounded">
                              {getSpecialCallText(ball.specialCall, ball.details)}
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-400">
                            {formatTime(ball.timestamp)}
                          </div>
                          <div className="text-sm text-gray-300">
                            Score: {ball.ballScores.team1} - {ball.ballScores.team2}
                          </div>
                        </div>
                      </div>
                      
                      {ball.points && (ball.points.team1 > 0 || ball.points.team2 > 0) && (
                        <div className="mt-2 text-sm text-gray-400">
                          Points: {teamNames[1]} {ball.points.team1} - {teamNames[2]} {ball.points.team2}
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
