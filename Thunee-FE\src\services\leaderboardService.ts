import { Competition, LeaderboardEntry, LeaderboardFilter } from "@/types/leaderboard";
import { apiService } from "./api";

// Legacy mock data - TODO: Remove when all components are updated to use API
const mockCompetitions: Competition[] = [
  {
    id: "comp-001",
    name: "Summer Championship 2025",
    description: "The biggest Thunee tournament of the summer! Join now to compete for amazing prizes.",
    startDate: "2025-06-01T00:00:00Z",
    endDate: "2025-08-31T23:59:59Z",
    status: "active",
    maxTeams: 64,
    currentTeams: 32,
    prizes: {
      first: "$500",
      second: "$250",
      third: "$100"
    }
  },
  {
    id: "comp-002",
    name: "Weekly Challenge Cup",
    description: "Weekly competition with new challenges every week.",
    startDate: "2025-05-15T00:00:00Z",
    endDate: "2025-05-22T23:59:59Z",
    status: "completed",
    maxTeams: 32,
    currentTeams: 32,
    prizes: {
      first: "$100",
      second: "$50",
      third: "$25"
    }
  },
  {
    id: "comp-003",
    name: "Beginner's Tournament",
    description: "Perfect for new players! Only players with less than 10 games can participate.",
    startDate: "2025-06-15T00:00:00Z",
    endDate: "2025-06-30T23:59:59Z",
    status: "upcoming",
    maxTeams: 16,
    currentTeams: 8,
    prizes: {
      first: "$200",
      second: "$100",
      third: "$50"
    }
  },
  {
    id: "comp-004",
    name: "Pro League Season 1",
    description: "The most competitive Thunee league for professional players.",
    startDate: "2025-07-01T00:00:00Z",
    endDate: "2025-09-30T23:59:59Z",
    status: "upcoming",
    maxTeams: 128,
    currentTeams: 45,
    prizes: {
      first: "$1000",
      second: "$500",
      third: "$250"
    }
  },
  {
    id: "comp-005",
    name: "Community Cup",
    description: "A friendly tournament organized by the community.",
    startDate: "2025-05-01T00:00:00Z",
    endDate: "2025-05-14T23:59:59Z",
    status: "completed",
    maxTeams: 24,
    currentTeams: 24,
    prizes: {
      first: "$150",
      second: "$75",
      third: "$25"
    }
  }
];

// Generate random leaderboard entries
const generateMockLeaderboard = (competitionId: string, count: number = 100): LeaderboardEntry[] => {
  const entries: LeaderboardEntry[] = [];
  
  for (let i = 0; i < count; i++) {
    const gamesPlayed = Math.floor(Math.random() * 50) + 10;
    const gamesWon = Math.floor(Math.random() * gamesPlayed);
    const winRate = (gamesWon / gamesPlayed) * 100;
    const score = gamesWon * 100 + Math.floor(Math.random() * 500);
    
    entries.push({
      id: `entry-${competitionId}-${i}`,
      playerId: `player-${Math.floor(Math.random() * 1000)}`,
      playerName: `Player ${Math.floor(Math.random() * 1000)}`,
      score,
      rank: i + 1,
      gamesPlayed,
      gamesWon,
      winRate
    });
  }
  
  // Sort by score descending
  entries.sort((a, b) => b.score - a.score);
  
  // Update ranks
  entries.forEach((entry, index) => {
    entry.rank = index + 1;
  });
  
  return entries;
};

// Mock leaderboard data cache
const mockLeaderboardCache: Record<string, LeaderboardEntry[]> = {};

// Service functions
export const leaderboardService = {
  // Get all competitions
  getCompetitions: async (): Promise<Competition[]> => {
    try {
      return await apiService.getCompetitions();
    } catch (error) {
      console.error('Failed to fetch competitions from API:', error);
      throw error; // Re-throw the error instead of using mock data
    }
  },

  // Get a specific competition by ID
  getCompetition: async (id: string): Promise<Competition | null> => {
    try {
      return await apiService.getCompetition(id);
    } catch (error) {
      console.error('Failed to fetch competition from API:', error);
      throw error; // Re-throw the error instead of using mock data
    }
  },
  
  // Get leaderboard for a competition with pagination and filters
  getLeaderboard: async (
    competitionId: string,
    page: number = 1,
    itemsPerPage: number = 10,
    filters?: LeaderboardFilter
  ): Promise<{
    entries: LeaderboardEntry[],
    totalItems: number
  }> => {
    try {
      // Call the API to get competition leaderboard
      const response = await apiService.getCompetitionLeaderboard(competitionId, page, itemsPerPage);

      // Map competition leaderboard entries to LeaderboardEntry format
      const entries: LeaderboardEntry[] = (response.data?.teams || []).map((team: any) => ({
        id: team.teamId,
        playerId: team.teamId, // Using teamId as playerId for competition leaderboard
        playerName: `${team.teamName} (${team.player1Name} & ${team.player2Name})`,
        score: team.totalPoints,
        rank: team.rank,
        gamesPlayed: team.gamesPlayed,
        gamesWon: team.gamesWon,
        winRate: team.winRate
      }));

      return {
        entries,
        totalItems: response.data?.pagination?.totalItems || 0
      };
    } catch (error) {
      console.error('Failed to fetch competition leaderboard from API:', error);
      throw error; // Re-throw the error instead of using mock data
    }
  },

  // Get global leaderboard
  getGlobalLeaderboard: async (
    timeFrame: string = 'all',
    sortBy: string = 'score',
    page: number = 1,
    limit: number = 20
  ): Promise<{
    entries: LeaderboardEntry[],
    totalItems: number,
    pagination: any,
    filters: any
  }> => {
    try {
      const response = await apiService.getGlobalLeaderboard(timeFrame, sortBy, page, limit);
      return {
        entries: response.data,
        totalItems: response.pagination.totalItems,
        pagination: response.pagination,
        filters: response.filters
      };
    } catch (error) {
      console.error('Failed to fetch global leaderboard from API:', error);
      throw error; // Re-throw the error instead of using mock data
    }
  },

  // Join a competition
  joinCompetition: async (competitionId: string): Promise<void> => {
    try {
      await apiService.joinCompetition(competitionId);
    } catch (error) {
      console.error('Failed to join competition:', error);
      throw error;
    }
  }
};
