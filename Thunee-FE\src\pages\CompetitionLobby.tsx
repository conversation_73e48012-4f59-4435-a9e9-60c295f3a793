import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Users, Play, Clock, Trophy } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { apiService } from '@/services/api';
import socketService from '@/services/socketService';
import BurgerMenu from '@/components/BurgerMenu';

interface CompetitionStatus {
  competitionId: string;
  competitionName: string;
  team?: {
    id: string;
    teamName: string;
    player1: { id: string; username: string };
    player2?: { id: string; username: string };
    gamesPlayed: number;
    maxGames: number;
    points: number;
    bonusPoints: number;
    isComplete: boolean;
  };
  hasTeam: boolean;
  canJoin: boolean;
  canResume: boolean;
  status: string;
}

export default function CompetitionLobby() {
  const { id: competitionId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuthStore();
  const [competitionStatus, setCompetitionStatus] = useState<CompetitionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creatingLobby, setCreatingLobby] = useState(false);
  const [partnerInviteCode, setPartnerInviteCode] = useState('');
  const [joiningLobby, setJoiningLobby] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!competitionId) {
      navigate('/competitions');
      return;
    }

    fetchCompetitionStatus();
  }, [competitionId, isAuthenticated, navigate]);

  const fetchCompetitionStatus = async () => {
    try {
      setLoading(true);
      const status = await apiService.getCompetitionStatus(competitionId!);
      setCompetitionStatus(status);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load competition status');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateLobby = async () => {
    if (!competitionStatus?.canResume) {
      setError('Cannot create lobby at this time');
      return;
    }

    setCreatingLobby(true);
    setError(null);

    try {
      // Connect to socket server if not already connected
      if (!socketService.isConnected()) {
        await socketService.connect(competitionStatus.team?.player1.username || 'Player');
      }

      // Create competition lobby via socket
      const response = await socketService.sendCustomEvent('create_competition_lobby', {
        competitionId: competitionId,
        teamName: competitionStatus.team?.teamName || 'Team 1'
      });

      if (response.success) {
        // Navigate to the game lobby with the competition lobby code
        // The socket events should handle updating the lobby store
        navigate('/lobby', {
          state: {
            competitionLobbyCode: response.lobbyCode,
            isCompetitionLobby: true,
            competitionId: response.competitionInfo?.competitionId || competitionId
          }
        });
      } else {
        setError(response.error || 'Failed to create competition lobby');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create lobby');
    } finally {
      setCreatingLobby(false);
    }
  };

  const handleJoinLobby = async () => {
    if (!partnerInviteCode.trim()) {
      setError('Please enter a partner invite code');
      return;
    }

    setJoiningLobby(true);
    setError(null);

    try {
      // Connect to socket server if not already connected
      if (!socketService.isConnected()) {
        await socketService.connect(user?.username || 'Player');
      }

      // Join lobby using the partner invite code
      const response = await socketService.joinLobby(partnerInviteCode.trim(), user?.username || 'Player');

      if (response) {
        // Navigate to the game lobby
        navigate('/lobby', {
          state: {
            competitionLobbyCode: response.actualLobbyCode || partnerInviteCode.trim(),
            isCompetitionLobby: true,
            competitionId: competitionId
          }
        });
      } else {
        setError('Failed to join lobby');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to join lobby');
    } finally {
      setJoiningLobby(false);
    }
  };

  const getStatusMessage = () => {
    if (!competitionStatus) return '';

    switch (competitionStatus.status) {
      case 'waiting_for_partner':
        return 'Waiting for your partner to join the competition';
      case 'ready_to_play':
        return 'Ready to start playing games';
      case 'in_progress':
        return 'Competition in progress';
      case 'completed':
        return 'All games completed for this competition';
      default:
        return 'Competition status unknown';
    }
  };

  const getStatusColor = () => {
    if (!competitionStatus) return 'text-gray-400';

    switch (competitionStatus.status) {
      case 'waiting_for_partner':
        return 'text-yellow-400';
      case 'ready_to_play':
        return 'text-green-400';
      case 'in_progress':
        return 'text-blue-400';
      case 'completed':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-dark text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E1C760] mx-auto mb-4"></div>
          <p>Loading competition status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark text-white flex flex-col">
      <BurgerMenu />
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/competitions')}
            className="mr-4 text-gray-400 hover:text-white"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-3xl font-bold text-[#E1C760]">
            Competition Lobby
          </h1>
        </div>

        {error && (
          <Alert className="mb-6 border-red-500 bg-red-900/20">
            <AlertDescription className="text-red-400">{error}</AlertDescription>
          </Alert>
        )}

        {competitionStatus && (
          <div className="space-y-6">
            {/* Competition Info */}
            <div className="bg-[#1A1A1A] border border-[#333333] rounded-lg p-6">
              <h2 className="text-xl font-semibold text-[#E1C760] mb-4 flex items-center">
                <Trophy className="h-5 w-5 mr-2" />
                {competitionStatus.competitionName}
              </h2>
              
              <div className={`text-sm ${getStatusColor()} mb-4`}>
                {getStatusMessage()}
              </div>

              {competitionStatus.team && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-white mb-2">Team Information</h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-400">Team Name:</span>
                        <span className="ml-2 text-white">{competitionStatus.team.teamName}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Player 1:</span>
                        <span className="ml-2 text-white">{competitionStatus.team.player1.username}</span>
                      </div>
                      {competitionStatus.team.player2 && (
                        <div>
                          <span className="text-gray-400">Player 2:</span>
                          <span className="ml-2 text-white">{competitionStatus.team.player2.username}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-white mb-2">Progress</h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-gray-400">Games Played:</span>
                        <span className="ml-2 text-white">
                          {competitionStatus.team.gamesPlayed} / {competitionStatus.team.maxGames}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-400">Points:</span>
                        <span className="ml-2 text-white">{competitionStatus.team.points}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Bonus Points:</span>
                        <span className="ml-2 text-white">{competitionStatus.team.bonusPoints}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Total Points:</span>
                        <span className="ml-2 text-[#E1C760] font-semibold">
                          {competitionStatus.team.points + competitionStatus.team.bonusPoints}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="bg-[#1A1A1A] border border-[#333333] rounded-lg p-6">
              <h3 className="font-semibold text-white mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Actions
              </h3>

              {competitionStatus.status === 'waiting_for_partner' && (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                  <p className="text-gray-300 mb-2">Waiting for your partner to join the competition</p>
                  <p className="text-sm text-gray-400">
                    Share your team invite code with your partner so they can join.
                  </p>
                </div>
              )}

              {competitionStatus.status === 'ready_to_play' && (
                <div className="text-center">
                  {/* Check if current user is Player 1 (team creator) or Player 2 */}
                  {competitionStatus.team?.player1.id === user?.id ? (
                    // Player 1 creates the lobby
                    <div>
                      <Button
                        onClick={handleCreateLobby}
                        disabled={creatingLobby}
                        className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90 px-8 py-3"
                      >
                        <Play className="h-5 w-5 mr-2" />
                        {creatingLobby ? 'Creating Lobby...' : 'Create Game Lobby'}
                      </Button>
                      <p className="text-sm text-gray-400 mt-2">
                        As team leader, create a lobby and share the partner invite code with your teammate.
                      </p>
                    </div>
                  ) : (
                    // Player 2 should join using partner invite code
                    <div>
                      <p className="text-gray-300 mb-4">
                        Your team leader ({competitionStatus.team?.player1.username}) should create the game lobby and share the partner invite code with you.
                      </p>
                      <div className="space-y-4">
                        <div>
                          <Input
                            type="text"
                            placeholder="Enter partner invite code"
                            value={partnerInviteCode}
                            onChange={(e) => setPartnerInviteCode(e.target.value.toUpperCase())}
                            className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                            maxLength={6}
                          />
                        </div>
                        <Button
                          onClick={handleJoinLobby}
                          disabled={joiningLobby || !partnerInviteCode.trim()}
                          className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90 px-8 py-3 w-full"
                        >
                          <Users className="h-5 w-5 mr-2" />
                          {joiningLobby ? 'Joining Lobby...' : 'Join Team Lobby'}
                        </Button>
                      </div>
                      <p className="text-sm text-gray-400 mt-2">
                        Enter the 6-character code your team leader shared with you.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {competitionStatus.status === 'completed' && (
                <div className="text-center py-8">
                  <Trophy className="h-12 w-12 text-[#E1C760] mx-auto mb-4" />
                  <p className="text-gray-300 mb-2">All games completed!</p>
                  <p className="text-sm text-gray-400">
                    Check the leaderboard to see your final ranking.
                  </p>
                  <Button
                    onClick={() => navigate(`/competitions/${competitionId}/leaderboard`)}
                    variant="outline"
                    className="mt-4 border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                  >
                    View Leaderboard
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
