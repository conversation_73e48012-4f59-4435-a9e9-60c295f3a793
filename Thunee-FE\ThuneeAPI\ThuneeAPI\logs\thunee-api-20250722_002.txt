2025-07-22 09:09:50.759 +02:00 [INF] Starting Thunee API Server
2025-07-22 09:10:29.075 +02:00 [INF] Login attempt for username: <PERSON>risan123
2025-07-22 09:10:30.699 +02:00 [INF] User logged in successfully: "ee9511df-a34b-46ec-b0b7-8bcd0e24373f"
2025-07-22 09:10:31.692 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:10:32.389 +02:00 [INF] Game settings retrieved successfully
2025-07-22 09:10:34.459 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserializeb6dd966b-74f6-4105-91e9-6a08088f4366(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserializeb6dd966b-74f6-4105-91e9-6a08088f4366(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
2025-07-22 09:10:35.052 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserializeb6dd966b-74f6-4105-91e9-6a08088f4366(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserializeb6dd966b-74f6-4105-91e9-6a08088f4366(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
2025-07-22 09:17:43.701 +02:00 [ERR] Error getting competitions
System.Data.DataException: Error parsing column 16 (MaxGamesPerTeam=10000000000 - Int64)
 ---> System.OverflowException: Arithmetic operation resulted in an overflow.
   at Deserializeb6dd966b-74f6-4105-91e9-6a08088f4366(DbDataReader)
   --- End of inner exception stack trace ---
   at Dapper.SqlMapper.ThrowDataException(Exception ex, Int32 index, IDataReader reader, Object value) in /_/Dapper/SqlMapper.cs:line 3928
   at Deserializeb6dd966b-74f6-4105-91e9-6a08088f4366(DbDataReader)
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command)
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters)
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync()
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync()
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions()
2025-07-22 09:20:03.397 +02:00 [INF] Starting Thunee API Server
